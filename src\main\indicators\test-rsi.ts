/**
 * Comprehensive test file to verify RSI implementation
 * Run this to ensure the RSI indicator system is working correctly
 */

import type { IndicatorDataPoint } from '../../shared/types/indicators'
import { createRSI, calculateRSI, IndicatorUtils } from './index'
import { logger } from '../../shared/utils/logger'

// Test constants
const FLOATING_POINT_PRECISION = 0.001
const PERFORMANCE_THRESHOLD_MS = 1
const ASYNC_TEST_TIMEOUT_MS = 100
const STREAM_UPDATE_DELAY_MS = 50

// Test data - Known RSI values for validation
const TEST_PRICES = [
  44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 46.69, 46.45,
  46.59, 46.3, 46.28, 46.28, 46.0, 46.03, 46.41, 46.22, 45.64
] as const
const EXPECTED_RSI_14 = [
  70.53, 66.32, 66.55, 69.41, 66.36, 57.97, 62.93, 63.26, 56.06, 62.38
] as const // Last 10 RSI values for period 14
const LARGE_DATASET_SIZE = 1000
const PERFORMANCE_TEST_PERIOD = 14

/**
 * Helper function to create test data points
 */
function createTestDataPoints(prices: readonly number[]): IndicatorDataPoint[] {
  return prices.map((price, index) => ({
    value: price,
    timestamp: Date.now() + index * 1000
  }))
}

/**
 * Helper function to compare arrays with floating point precision
 */
function areArraysApproximatelyEqual(
  actual: number[],
  expected: readonly number[],
  precision: number = FLOATING_POINT_PRECISION
): boolean {
  if (actual.length !== expected.length) return false

  return actual.every((value, index) => Math.abs(value - expected[index]) < precision)
}

/**
 * Test basic RSI calculation
 */
function testBasicRSI(): boolean {
  console.log('Testing basic RSI calculation...')

  try {
    const rsi = createRSI({ period: 14 })
    const testDataPoints = createTestDataPoints(TEST_PRICES)

    testDataPoints.forEach((dataPoint) => {
      rsi.addData(dataPoint)
    })

    const values = IndicatorUtils.extractValues(rsi.getValues())
    const lastTenValues = values.slice(-10)

    console.log(
      'Calculated RSI values (last 10):',
      lastTenValues.map((v) => v.toFixed(2))
    )
    console.log(
      'Expected RSI values (last 10):',
      EXPECTED_RSI_14.map((v) => v.toFixed(2))
    )

    const isCorrect = areArraysApproximatelyEqual(lastTenValues, EXPECTED_RSI_14, 1.0) // Allow 1.0 precision for RSI

    if (isCorrect) {
      console.log('✅ Basic RSI test passed')
      return true
    } else {
      console.log('❌ Basic RSI test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Basic RSI test error:', error)
    return false
  }
}

/**
 * Test legacy function compatibility
 */
function testLegacyFunction(): boolean {
  console.log('\nTesting legacy function compatibility...')

  try {
    const period = 14
    const values = calculateRSI([...TEST_PRICES], period)
    const lastTenValues = values.slice(-10)

    console.log(
      'Legacy function RSI values (last 10):',
      lastTenValues.map((v) => v.toFixed(2))
    )
    console.log(
      'Expected RSI values (last 10):',
      EXPECTED_RSI_14.map((v) => v.toFixed(2))
    )

    const isCorrect = areArraysApproximatelyEqual(lastTenValues, EXPECTED_RSI_14, 1.0)

    if (isCorrect) {
      console.log('✅ Legacy function test passed')
      return true
    } else {
      console.log('❌ Legacy function test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Legacy function test error:', error)
    return false
  }
}

/**
 * Test performance optimization
 */
function testPerformance(): boolean {
  console.log('\nTesting performance optimization...')

  try {
    // Generate large dataset
    const largePrices = Array.from(
      { length: LARGE_DATASET_SIZE },
      (_, i) => 50 + Math.sin(i * 0.1) * 10 + Math.random() * 2
    )

    const rsi = createRSI({ period: PERFORMANCE_TEST_PERIOD })
    const testDataPoints = createTestDataPoints(largePrices)

    const startTime = performance.now()

    testDataPoints.forEach((dataPoint) => {
      rsi.addData(dataPoint)
    })

    const endTime = performance.now()
    const executionTime = endTime - startTime

    console.log(
      `Performance test: ${LARGE_DATASET_SIZE} data points processed in ${executionTime.toFixed(2)}ms`
    )
    console.log(
      `Average time per calculation: ${(executionTime / LARGE_DATASET_SIZE).toFixed(4)}ms`
    )

    const isPerfAcceptable = executionTime / LARGE_DATASET_SIZE < PERFORMANCE_THRESHOLD_MS

    if (isPerfAcceptable) {
      console.log('✅ Performance test passed')
      return true
    } else {
      console.log('❌ Performance test failed - execution too slow')
      return false
    }
  } catch (error) {
    console.error('❌ Performance test error:', error)
    return false
  }
}

/**
 * Test error handling
 */
function testErrorHandling(): boolean {
  console.log('\nTesting error handling...')

  try {
    let testsPassed = 0
    const totalTests = 4

    // Test 1: Invalid period
    try {
      createRSI({ period: 0 })
      console.log('❌ Should have thrown error for invalid period')
    } catch (error) {
      console.log('✅ Correctly handled invalid period')
      testsPassed++
    }

    // Test 2: Invalid data point
    try {
      const rsi = createRSI({ period: 14 })
      rsi.addData({ value: NaN, timestamp: Date.now() })
      console.log('❌ Should have thrown error for NaN value')
    } catch (error) {
      console.log('✅ Correctly handled NaN value')
      testsPassed++
    }

    // Test 3: Insufficient data
    const rsi = createRSI({ period: 14 })
    const result = rsi.addData({ value: 100, timestamp: Date.now() })

    if (result === null) {
      console.log('✅ Correctly returned null for insufficient data')
      testsPassed++
    } else {
      console.log('❌ Should have returned null for insufficient data')
    }

    // Test 4: Empty data array for legacy function
    try {
      const emptyResult = calculateRSI([], 14)
      if (emptyResult.length === 0) {
        console.log('✅ Correctly handled empty array')
        testsPassed++
      } else {
        console.log('❌ Should have returned empty array')
      }
    } catch (error) {
      console.log('✅ Correctly handled empty array with error')
      testsPassed++
    }

    const allTestsPassed = testsPassed === totalTests

    if (allTestsPassed) {
      console.log('✅ Error handling test passed')
      return true
    } else {
      console.log(`❌ Error handling test failed (${testsPassed}/${totalTests} passed)`)
      return false
    }
  } catch (error) {
    console.error('❌ Error handling test error:', error)
    return false
  }
}

/**
 * Test streaming functionality
 */
async function testStreaming(): Promise<boolean> {
  console.log('\nTesting streaming functionality...')

  return new Promise((resolve) => {
    try {
      const rsi = createRSI({ period: 5, enableStreaming: true })
      let streamUpdateCount = 0
      let lastStreamValue: number | null = null

      // Set up streaming callback
      rsi.onStreamUpdate((update) => {
        streamUpdateCount++
        lastStreamValue = update.indicatorValue.value
        console.log(`📊 Stream update ${streamUpdateCount}: RSI = ${lastStreamValue.toFixed(2)}`)
      })

      // Add test data with delays to simulate real-time
      const testData = createTestDataPoints([45, 46, 44, 47, 45, 48, 46])
      let dataIndex = 0

      const addDataWithDelay = () => {
        if (dataIndex < testData.length) {
          rsi.addData(testData[dataIndex])
          dataIndex++
          setTimeout(addDataWithDelay, STREAM_UPDATE_DELAY_MS)
        } else {
          // Test completed
          setTimeout(() => {
            const expectedUpdates = testData.length - 5 // Should get updates after period
            const hasCorrectUpdates = streamUpdateCount === expectedUpdates
            const hasValidValue =
              lastStreamValue !== null && lastStreamValue >= 0 && lastStreamValue <= 100

            if (hasCorrectUpdates && hasValidValue) {
              console.log('✅ Streaming test passed')
              resolve(true)
            } else {
              console.log(
                `❌ Streaming test failed (updates: ${streamUpdateCount}, expected: ${expectedUpdates}, lastValue: ${lastStreamValue})`
              )
              resolve(false)
            }
          }, ASYNC_TEST_TIMEOUT_MS)
        }
      }

      addDataWithDelay()
    } catch (error) {
      console.error('❌ Streaming test error:', error)
      resolve(false)
    }
  })
}

/**
 * Test threshold detection (overbought/oversold)
 */
function testThresholdDetection(): boolean {
  console.log('\nTesting threshold detection...')

  try {
    const rsi = createRSI({
      period: 5,
      overboughtThreshold: 70,
      oversoldThreshold: 30
    })

    // Create data that will generate high RSI (overbought)
    const overboughtData = [40, 45, 50, 55, 60, 65]
    overboughtData.forEach((price) => {
      rsi.addData({ value: price, timestamp: Date.now() })
    })

    const isOverbought = rsi.isOverbought()
    const currentRSI = rsi.getCurrentRSI()

    console.log(`Current RSI: ${currentRSI?.toFixed(2)}, Overbought: ${isOverbought}`)

    // Reset and test oversold
    const rsi2 = createRSI({
      period: 5,
      overboughtThreshold: 70,
      oversoldThreshold: 30
    })

    // Create data that will generate low RSI (oversold)
    const oversoldData = [60, 55, 50, 45, 40, 35]
    oversoldData.forEach((price) => {
      rsi2.addData({ value: price, timestamp: Date.now() })
    })

    const isOversold = rsi2.isOversold()
    const currentRSI2 = rsi2.getCurrentRSI()

    console.log(`Current RSI: ${currentRSI2?.toFixed(2)}, Oversold: ${isOversold}`)

    // Validate threshold detection logic
    const thresholdTestPassed =
      currentRSI !== null &&
      currentRSI2 !== null &&
      isOverbought === currentRSI > 70 &&
      isOversold === currentRSI2 < 30

    if (thresholdTestPassed) {
      console.log('✅ Threshold detection test passed')
      return true
    } else {
      console.log('❌ Threshold detection test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Threshold detection test error:', error)
    return false
  }
}

/**
 * Test EMA smoothing method
 */
function testEMASmoothing(): boolean {
  console.log('\nTesting EMA smoothing method...')

  try {
    const rsiSMA = createRSI({ period: 14, smoothingMethod: 'sma' })
    const rsiEMA = createRSI({ period: 14, smoothingMethod: 'ema' })

    const testDataPoints = createTestDataPoints(TEST_PRICES)

    // Add same data to both indicators
    testDataPoints.forEach((dataPoint) => {
      rsiSMA.addData(dataPoint)
      rsiEMA.addData(dataPoint)
    })

    const smaValues = IndicatorUtils.extractValues(rsiSMA.getValues())
    const emaValues = IndicatorUtils.extractValues(rsiEMA.getValues())

    console.log(`SMA RSI (last): ${smaValues[smaValues.length - 1]?.toFixed(2)}`)
    console.log(`EMA RSI (last): ${emaValues[emaValues.length - 1]?.toFixed(2)}`)

    // Both should have values and they should be different (EMA is more responsive)
    const hasValues = smaValues.length > 0 && emaValues.length > 0
    const areDifferent =
      Math.abs(smaValues[smaValues.length - 1] - emaValues[emaValues.length - 1]) > 0.1

    if (hasValues && areDifferent) {
      console.log('✅ EMA smoothing test passed')
      return true
    } else {
      console.log('❌ EMA smoothing test failed')
      return false
    }
  } catch (error) {
    console.error('❌ EMA smoothing test error:', error)
    return false
  }
}

/**
 * Test suite configuration
 */
interface TestSuite {
  name: string
  testFunction: () => boolean | Promise<boolean>
}

/**
 * All test suites to run
 */
const TEST_SUITES: readonly TestSuite[] = [
  { name: 'Basic RSI Calculation', testFunction: testBasicRSI },
  { name: 'Legacy Function Compatibility', testFunction: testLegacyFunction },
  { name: 'Performance Optimization', testFunction: testPerformance },
  { name: 'Error Handling', testFunction: testErrorHandling },
  { name: 'Streaming Functionality', testFunction: testStreaming },
  { name: 'Threshold Detection', testFunction: testThresholdDetection },
  { name: 'EMA Smoothing Method', testFunction: testEMASmoothing }
] as const

/**
 * Run all RSI tests
 */
export async function runTests(): Promise<boolean> {
  console.log('🚀 Starting RSI Indicator Test Suite...\n')

  let passedTests = 0
  const totalTests = TEST_SUITES.length

  for (const testSuite of TEST_SUITES) {
    try {
      console.log(`\n📋 Running: ${testSuite.name}`)
      console.log('─'.repeat(50))

      const result = await testSuite.testFunction()

      if (result) {
        passedTests++
        console.log(`✅ ${testSuite.name} - PASSED`)
      } else {
        console.log(`❌ ${testSuite.name} - FAILED`)
      }
    } catch (error) {
      console.error(`💥 ${testSuite.name} - ERROR:`, error)
    }
  }

  console.log('\n' + '='.repeat(60))
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`)

  if (passedTests === totalTests) {
    console.log('🎉 All RSI tests passed! The indicator is working correctly.')
    return true
  } else {
    console.log('⚠️  Some RSI tests failed. Please review the implementation.')
    return false
  }
}

/**
 * Quick verification function for manual testing
 */
export function quickTest(): void {
  console.log('🚀 Quick RSI Test...')

  const QUICK_TEST_PERIOD = 5
  const QUICK_TEST_PRICES = [100, 102, 98, 105, 103, 99, 101, 104, 97, 106] as const

  const rsi = createRSI({ period: QUICK_TEST_PERIOD })
  const testDataPoints = createTestDataPoints(QUICK_TEST_PRICES)

  console.log('Input prices:', QUICK_TEST_PRICES)

  testDataPoints.forEach((dataPoint, index) => {
    const result = rsi.addData(dataPoint)
    if (result) {
      console.log(`Price: ${QUICK_TEST_PRICES[index]}, RSI: ${result.value.toFixed(2)}`)

      if (rsi.isOverbought()) {
        console.log('  🔴 OVERBOUGHT signal')
      } else if (rsi.isOversold()) {
        console.log('  🟢 OVERSOLD signal')
      }
    }
  })

  console.log('✅ Quick test completed')
}

/**
 * Public API for the test module
 */
export const RSITestSuite = {
  // Main test functions
  runTests,
  quickTest,

  // Individual test functions (for selective testing)
  testBasicRSI,
  testLegacyFunction,
  testPerformance,
  testStreaming,
  testErrorHandling,
  testThresholdDetection,
  testEMASmoothing,

  // Utility functions
  areArraysApproximatelyEqual,
  createTestDataPoints,

  // Test constants (for external use)
  constants: {
    FLOATING_POINT_PRECISION,
    PERFORMANCE_THRESHOLD_MS,
    ASYNC_TEST_TIMEOUT_MS,
    STREAM_UPDATE_DELAY_MS,
    TEST_PRICES,
    EXPECTED_RSI_14,
    LARGE_DATASET_SIZE,
    PERFORMANCE_TEST_PERIOD
  }
} as const

// Auto-run tests if this file is executed directly
if (require.main === module) {
  runTests()
    .then((success) => {
      process.exit(success ? 0 : 1)
    })
    .catch((error) => {
      console.error('Test execution failed:', error)
      process.exit(1)
    })
}
